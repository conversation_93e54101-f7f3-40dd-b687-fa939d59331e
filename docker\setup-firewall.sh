#!/bin/bash

# Ubuntu 防火墙配置脚本 for Dify
# ==============================

set -e

echo "🔥 配置 Ubuntu 防火墙 for Dify 服务..."

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请以 root 用户运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

# 显示当前防火墙状态
echo "📊 当前防火墙状态:"
ufw status verbose
echo ""

# 启用防火墙（如果未启用）
echo "🔧 启用防火墙..."
ufw --force enable

# 设置默认策略
echo "🛡️  设置默认策略..."
ufw default deny incoming
ufw default allow outgoing

# 允许 SSH 连接（重要！避免被锁定）
echo "🔑 允许 SSH 连接..."
ufw allow ssh
ufw allow 22/tcp

# 允许基本服务端口
echo "🌐 配置基本服务端口..."

# HTTP/HTTPS
ufw allow 80/tcp comment 'HTTP'
ufw allow 443/tcp comment 'HTTPS'

# Dify 服务端口
echo "🚀 配置 Dify 服务端口..."

# Nginx 主端口
ufw allow 8380/tcp comment 'Dify Nginx Main Port'

# Console API 端口
ufw allow 8381/tcp comment 'Dify Console API Port'

# PostgreSQL 端口（如果需要外部访问）
ufw allow 15432/tcp comment 'Dify PostgreSQL Port'

# Redis 端口（通常不需要外部访问，但如果需要）
# ufw allow 16379/tcp comment 'Dify Redis Port'

# API 直接访问端口（通常通过 nginx 代理，不需要直接暴露）
# ufw allow 5001/tcp comment 'Dify API Direct Port'

# 允许内网访问（根据您的网络调整）
echo "🏠 配置内网访问..."
ufw allow from 10.118.1.0/24 comment 'Internal Network Access'

# Docker 相关端口（如果需要）
echo "🐳 配置 Docker 相关端口..."
# Docker daemon API（通常不需要外部访问）
# ufw allow 2376/tcp comment 'Docker Daemon API'

# 显示配置结果
echo ""
echo "✅ 防火墙配置完成！"
echo "==================="
echo ""
echo "📋 当前防火墙规则:"
ufw status numbered
echo ""

echo "🔍 验证端口开放状态:"
echo "===================="
echo "检查端口是否开放:"
echo "  - HTTP (80): $(ufw status | grep -q '80/tcp' && echo '✅ 开放' || echo '❌ 关闭')"
echo "  - HTTPS (443): $(ufw status | grep -q '443/tcp' && echo '✅ 开放' || echo '❌ 关闭')"
echo "  - Dify Main (8380): $(ufw status | grep -q '8380/tcp' && echo '✅ 开放' || echo '❌ 关闭')"
echo "  - Dify API (8381): $(ufw status | grep -q '8381/tcp' && echo '✅ 开放' || echo '❌ 关闭')"
echo "  - PostgreSQL (15432): $(ufw status | grep -q '15432/tcp' && echo '✅ 开放' || echo '❌ 关闭')"
echo ""

echo "💡 测试命令:"
echo "============"
echo "从外部测试端口连通性:"
echo "  telnet 10.118.1.153 8380"
echo "  telnet 10.118.1.153 8381"
echo "  curl http://10.118.1.153:8380"
echo "  curl http://10.118.1.153:8381"
echo ""

echo "⚠️  重要提醒:"
echo "============"
echo "1. SSH 端口 (22) 已开放，确保不会被锁定"
echo "2. 内网 10.118.1.0/24 已允许访问"
echo "3. 如需修改规则，使用: sudo ufw delete [规则编号]"
echo "4. 重新加载防火墙: sudo ufw reload"
