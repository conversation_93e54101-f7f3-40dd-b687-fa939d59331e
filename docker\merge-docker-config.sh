#!/bin/bash

# Docker 配置合并脚本
# ===================

set -e

echo "🔧 合并 Docker daemon 配置..."

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请以 root 用户运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

# 备份当前配置
if [ -f /etc/docker/daemon.json ]; then
    cp /etc/docker/daemon.json /etc/docker/daemon.json.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ 已备份当前配置"
fi

# 创建临时配置文件
TEMP_CONFIG="/tmp/daemon.json.new"

# 检查现有配置
if [ -f /etc/docker/daemon.json ]; then
    echo "📋 读取现有配置..."
    cat /etc/docker/daemon.json
    echo ""
    
    # 使用 Python 脚本合并配置
    python3 << 'EOF'
import json
import os

# 读取现有配置
existing_config = {}
if os.path.exists('/etc/docker/daemon.json'):
    try:
        with open('/etc/docker/daemon.json', 'r') as f:
            existing_config = json.load(f)
    except:
        existing_config = {}

# 新的网络配置
new_config = {
    "default-address-pools": [
        {
            "base": "***********/16",
            "size": 24
        },
        {
            "base": "**********/16", 
            "size": 24
        }
    ],
    "bip": "*************/24",
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "10m",
        "max-file": "3"
    },
    "storage-driver": "overlay2"
}

# 合并配置，保留现有的 registry-mirrors
merged_config = existing_config.copy()
merged_config.update(new_config)

# 如果没有 registry-mirrors，添加默认的
if "registry-mirrors" not in merged_config:
    merged_config["registry-mirrors"] = [
        "https://docker.mirrors.sjtug.sjtu.edu.cn",
        "https://docker.m.daocloud.io",
        "https://docker.itelyou.cf",
        "https://noohub.ru",
        "https://docker.fxxk.dedyn.io",
        "https://huecker.io",
        "https://dockerhub.timeweb.cloud",
        "https://registry.cn-hangzhou.aliyuncs.com"
    ]

# 写入新配置
with open('/tmp/daemon.json.new', 'w') as f:
    json.dump(merged_config, f, indent=2)

print("✅ 配置合并完成")
EOF

else
    echo "📋 创建新的配置文件..."
    # 创建全新的配置
    cat > "$TEMP_CONFIG" << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.sjtug.sjtu.edu.cn",
    "https://docker.m.daocloud.io",
    "https://docker.itelyou.cf",
    "https://noohub.ru",
    "https://docker.fxxk.dedyn.io",
    "https://huecker.io",
    "https://dockerhub.timeweb.cloud",
    "https://registry.cn-hangzhou.aliyuncs.com"
  ],
  "default-address-pools": [
    {
      "base": "***********/16",
      "size": 24
    },
    {
      "base": "**********/16", 
      "size": 24
    }
  ],
  "bip": "*************/24",
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2"
}
EOF
fi

# 验证 JSON 格式
echo "🔍 验证新配置格式..."
if python3 -m json.tool "$TEMP_CONFIG" > /dev/null; then
    echo "✅ JSON 格式正确"
else
    echo "❌ JSON 格式错误"
    exit 1
fi

# 显示新配置
echo ""
echo "📋 新的配置内容:"
echo "================"
cat "$TEMP_CONFIG"
echo ""

# 确认应用
read -p "确认应用新配置？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    # 应用新配置
    mkdir -p /etc/docker
    cp "$TEMP_CONFIG" /etc/docker/daemon.json
    rm "$TEMP_CONFIG"
    
    echo "✅ 新配置已应用"
    echo ""
    echo "💡 下一步:"
    echo "  1. 重启 Docker: sudo systemctl restart docker"
    echo "  2. 重启 Dify: docker-compose down && docker-compose up -d"
else
    echo "❌ 操作已取消"
    rm "$TEMP_CONFIG"
fi
