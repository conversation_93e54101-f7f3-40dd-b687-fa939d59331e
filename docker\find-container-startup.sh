#!/bin/bash

# 查找容器启动位置脚本
# ===================

echo "🔍 查找容器启动位置..."

# 目标容器
CONTAINERS=("sweet_dijkstra" "mysql-dev-test")
IMAGES=("showdoc:ctsy" "mysql:5.7.44")

echo "📊 目标容器信息:"
echo "================"
for container in "${CONTAINERS[@]}"; do
    if docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}" | grep -q "$container"; then
        echo "✅ 找到容器: $container"
        docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}" | grep "$container"
    else
        echo "❌ 未找到容器: $container"
    fi
done
echo ""

echo "🔍 搜索启动脚本..."
echo "=================="

# 搜索路径
SEARCH_PATHS=("/home" "/root" "/opt" "/data" "/data5" "/usr/local" "/etc")

for path in "${SEARCH_PATHS[@]}"; do
    if [ -d "$path" ]; then
        echo "搜索路径: $path"
        
        # 搜索包含镜像名的脚本
        find "$path" -name "*.sh" -type f 2>/dev/null | while read -r file; do
            if grep -l "showdoc\|mysql:5.7.44\|sweet_dijkstra\|mysql-dev-test" "$file" 2>/dev/null; then
                echo "  📄 找到相关脚本: $file"
                echo "     内容预览:"
                grep -n -A2 -B2 "showdoc\|mysql:5.7.44\|sweet_dijkstra\|mysql-dev-test" "$file" 2>/dev/null | head -10
                echo "     ---"
            fi
        done
    fi
done

echo ""
echo "🔍 检查系统服务..."
echo "=================="

# 检查 systemd 服务
echo "SystemD 服务:"
systemctl list-units --type=service --all | grep -i -E "(showdoc|mysql|docker)" || echo "  无相关服务"

echo ""
echo "🔍 检查定时任务..."
echo "=================="

# 检查 crontab
echo "当前用户的 crontab:"
crontab -l 2>/dev/null | grep -i docker || echo "  无相关定时任务"

echo ""
echo "Root 用户的 crontab:"
sudo crontab -l 2>/dev/null | grep -i docker || echo "  无相关定时任务"

echo ""
echo "🔍 检查历史命令..."
echo "=================="

# 检查历史命令
echo "当前用户历史命令:"
history | grep -E "docker run.*(showdoc|mysql)" | tail -5 || echo "  无相关历史"

echo ""
echo "Root 用户历史命令:"
sudo cat /root/.bash_history 2>/dev/null | grep -E "docker run.*(showdoc|mysql)" | tail -5 || echo "  无相关历史或无权限"

echo ""
echo "🔍 检查容器详细信息..."
echo "===================="

for container in "${CONTAINERS[@]}"; do
    if docker ps -a -q -f name="$container" | grep -q .; then
        echo "容器 $container 的详细信息:"
        echo "创建时间: $(docker inspect --format='{{.Created}}' "$container" 2>/dev/null)"
        echo "启动命令: $(docker inspect --format='{{.Config.Cmd}}' "$container" 2>/dev/null)"
        echo "环境变量: $(docker inspect --format='{{.Config.Env}}' "$container" 2>/dev/null)"
        echo "挂载点: $(docker inspect --format='{{.Mounts}}' "$container" 2>/dev/null)"
        echo "---"
    fi
done

echo ""
echo "🔍 检查 Docker Compose 文件..."
echo "============================="

# 搜索 docker-compose 文件
find /home /root /opt /data* -name "docker-compose*.yml" -o -name "docker-compose*.yaml" 2>/dev/null | while read -r file; do
    if grep -l "showdoc\|mysql:5.7.44" "$file" 2>/dev/null; then
        echo "📄 找到相关 compose 文件: $file"
        echo "   内容预览:"
        grep -n -A5 -B2 "showdoc\|mysql:5.7.44" "$file" 2>/dev/null
        echo "   ---"
    fi
done

echo ""
echo "💡 建议操作:"
echo "============"
echo "1. 如果找到了启动脚本，请检查脚本内容"
echo "2. 如果是手动启动的，可以通过以下命令重建启动脚本:"
echo ""
echo "# 生成 showdoc 容器的启动命令"
echo "docker run --rm -v /var/run/docker.sock:/var/run/docker.sock assaflavie/runlike sweet_dijkstra"
echo ""
echo "# 生成 mysql 容器的启动命令"  
echo "docker run --rm -v /var/run/docker.sock:/var/run/docker.sock assaflavie/runlike mysql-dev-test"
echo ""
echo "3. 或者查看容器的完整配置:"
echo "docker inspect sweet_dijkstra > sweet_dijkstra_config.json"
echo "docker inspect mysql-dev-test > mysql-dev-test_config.json"
