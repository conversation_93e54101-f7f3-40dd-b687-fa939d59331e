#!/bin/bash

# Docker 路由冲突修复脚本
# =======================

set -e

echo "🔧 修复 Docker 路由冲突..."

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请以 root 用户运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

echo "📊 当前路由表:"
echo "=============="
route -n
echo ""

# 添加更具体的路由规则，优先级高于 Docker 的广泛路由
echo "🛣️  添加具体的物理网络路由..."

# 删除可能存在的旧规则
ip route del ************/24 via ************ 2>/dev/null || true
ip route del ************/24 via ************ 2>/dev/null || true

# 添加具体的物理网络路由（优先级高于 Docker 的 /16 路由）
ip route add ************/24 via ************ dev eno1 metric 100
ip route add ************/24 via ************ dev eno1 metric 100

echo "✅ 路由规则已添加"

echo ""
echo "📊 修复后的路由表:"
echo "=================="
route -n
echo ""

# 测试连通性
echo "🔍 测试连通性..."
echo "================"

# 测试到物理网络的连通性
echo "测试到 ************:"
ping -c 2 ************ && echo "✅ 连通正常" || echo "❌ 连通失败"

echo ""
echo "测试到 ************ 网段:"
ping -c 2 ************ && echo "✅ 连通正常" || echo "❌ 连通失败"

echo ""
echo "🎉 路由修复完成！"
echo "================"
echo ""
echo "💡 现在可以测试:"
echo "  从 ************ 执行: telnet ************ 8380"
echo ""
echo "⚠️  注意: 这是临时修复，重启后需要重新执行"
echo "建议使用永久解决方案（重新配置 Docker 网络）"
