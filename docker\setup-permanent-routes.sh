#!/bin/bash

# 永久路由配置脚本
# ================

set -e

echo "🛣️  配置永久路由规则..."

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请以 root 用户运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

# 创建网络配置文件
echo "📝 创建永久路由配置..."

# 备份现有配置
if [ -f /etc/netplan/01-netcfg.yaml ]; then
    cp /etc/netplan/01-netcfg.yaml /etc/netplan/01-netcfg.yaml.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ 已备份现有网络配置"
fi

# 创建路由配置文件
cat > /etc/systemd/system/fix-docker-routes.service << 'EOF'
[Unit]
Description=Fix Docker Network Routes
After=docker.service
Requires=docker.service

[Service]
Type=oneshot
ExecStart=/usr/local/bin/fix-docker-routes.sh
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

# 创建路由修复脚本
cat > /usr/local/bin/fix-docker-routes.sh << 'EOF'
#!/bin/bash

# 等待网络接口就绪
sleep 10

# 添加具体的物理网络路由
ip route add ************/24 via ************ dev eno1 metric 100 2>/dev/null || true
ip route add ************/24 via ************ dev eno1 metric 100 2>/dev/null || true

# 记录日志
echo "$(date): Docker routes fixed" >> /var/log/docker-routes.log
EOF

# 设置执行权限
chmod +x /usr/local/bin/fix-docker-routes.sh

# 启用服务
systemctl enable fix-docker-routes.service

echo "✅ 永久路由配置已创建"

# 立即应用路由修复
echo "🔧 立即应用路由修复..."
/usr/local/bin/fix-docker-routes.sh

echo ""
echo "📊 当前路由表:"
echo "=============="
route -n
echo ""

echo "🎉 永久路由配置完成！"
echo "===================="
echo ""
echo "📋 配置详情:"
echo "  - 服务文件: /etc/systemd/system/fix-docker-routes.service"
echo "  - 脚本文件: /usr/local/bin/fix-docker-routes.sh"
echo "  - 日志文件: /var/log/docker-routes.log"
echo ""
echo "🔧 管理命令:"
echo "  - 查看服务状态: systemctl status fix-docker-routes"
echo "  - 手动执行: /usr/local/bin/fix-docker-routes.sh"
echo "  - 查看日志: tail -f /var/log/docker-routes.log"
echo ""
echo "💡 现在可以测试:"
echo "  从 172.18.185.3 执行: telnet 10.118.1.153 8380"
