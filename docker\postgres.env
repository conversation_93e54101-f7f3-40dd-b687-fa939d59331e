# PostgreSQL Database Configuration for Dify
# ==========================================

# Database Connection Settings
DB_USERNAME=dify_root
DB_PASSWORD=dify_root@ctsy
DB_HOST=db
DB_PORT=15432
DB_DATABASE=dify_db

# PostgreSQL Admin Settings
POSTGRES_ADMIN_PASSWORD=postgres123456

# PostgreSQL Performance Tuning
POSTGRES_MAX_CONNECTIONS=100
POSTGRES_SHARED_BUFFERS=128MB
POSTGRES_WORK_MEM=4MB
POSTGRES_MAINTENANCE_WORK_MEM=64MB
POSTGRES_EFFECTIVE_CACHE_SIZE=4096MB

# Data Directory Settings
PGDATA=/var/lib/postgresql/data/pgdata

# Volume Mappings (Host Paths)
PGDATA_HOST_VOLUME=./volumes/postgres/data
PGLOG_HOST_VOLUME=./volumes/postgres/logs
PGINIT_HOST_VOLUME=./volumes/postgres/init

# Port Exposure
EXPOSE_POSTGRES_PORT=15432
