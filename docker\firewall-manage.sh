#!/bin/bash

# Ubuntu 防火墙管理脚本
# ====================

# 显示帮助信息
show_help() {
    echo "Ubuntu 防火墙管理工具"
    echo "===================="
    echo ""
    echo "用法: $0 [命令] [参数]"
    echo ""
    echo "命令:"
    echo "  status          - 查看防火墙状态"
    echo "  list            - 列出所有规则"
    echo "  enable          - 启用防火墙"
    echo "  disable         - 禁用防火墙"
    echo "  allow [port]    - 允许指定端口"
    echo "  deny [port]     - 拒绝指定端口"
    echo "  delete [num]    - 删除指定编号的规则"
    echo "  reset           - 重置所有规则"
    echo "  test [port]     - 测试端口连通性"
    echo "  dify-ports      - 显示 Dify 相关端口"
    echo "  help            - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 status"
    echo "  $0 allow 8080"
    echo "  $0 test 8380"
    echo "  $0 delete 5"
    echo ""
}

# 查看防火墙状态
check_status() {
    echo "🔍 防火墙状态:"
    echo "=============="
    sudo ufw status verbose
    echo ""
    echo "📊 规则统计:"
    echo "============"
    local total_rules=$(sudo ufw status numbered | grep -c "^\[")
    local allow_rules=$(sudo ufw status | grep -c "ALLOW")
    local deny_rules=$(sudo ufw status | grep -c "DENY")
    echo "总规则数: $total_rules"
    echo "允许规则: $allow_rules"
    echo "拒绝规则: $deny_rules"
}

# 列出所有规则
list_rules() {
    echo "📋 所有防火墙规则:"
    echo "=================="
    sudo ufw status numbered
}

# 启用防火墙
enable_firewall() {
    echo "🔧 启用防火墙..."
    sudo ufw --force enable
    echo "✅ 防火墙已启用"
}

# 禁用防火墙
disable_firewall() {
    echo "⚠️  禁用防火墙..."
    read -p "确认禁用防火墙？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        sudo ufw disable
        echo "✅ 防火墙已禁用"
    else
        echo "❌ 操作已取消"
    fi
}

# 允许端口
allow_port() {
    local port="$1"
    if [ -z "$port" ]; then
        echo "❌ 请指定端口号"
        echo "用法: $0 allow <端口号>"
        exit 1
    fi
    
    echo "🔓 允许端口 $port..."
    sudo ufw allow "$port"
    echo "✅ 端口 $port 已允许"
}

# 拒绝端口
deny_port() {
    local port="$1"
    if [ -z "$port" ]; then
        echo "❌ 请指定端口号"
        echo "用法: $0 deny <端口号>"
        exit 1
    fi
    
    echo "🔒 拒绝端口 $port..."
    sudo ufw deny "$port"
    echo "✅ 端口 $port 已拒绝"
}

# 删除规则
delete_rule() {
    local rule_num="$1"
    if [ -z "$rule_num" ]; then
        echo "❌ 请指定规则编号"
        echo "用法: $0 delete <规则编号>"
        echo ""
        echo "当前规则:"
        sudo ufw status numbered
        exit 1
    fi
    
    echo "🗑️  删除规则 $rule_num..."
    sudo ufw --force delete "$rule_num"
    echo "✅ 规则已删除"
}

# 重置防火墙
reset_firewall() {
    echo "⚠️  重置防火墙将删除所有规则！"
    read -p "确认重置？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        sudo ufw --force reset
        echo "✅ 防火墙已重置"
    else
        echo "❌ 操作已取消"
    fi
}

# 测试端口连通性
test_port() {
    local port="$1"
    if [ -z "$port" ]; then
        echo "❌ 请指定端口号"
        echo "用法: $0 test <端口号>"
        exit 1
    fi
    
    echo "🔍 测试端口 $port 连通性..."
    echo "=========================="
    
    # 检查端口是否在监听
    if netstat -tuln | grep -q ":$port "; then
        echo "✅ 端口 $port 正在监听"
    else
        echo "❌ 端口 $port 未在监听"
    fi
    
    # 检查防火墙规则
    if sudo ufw status | grep -q "$port"; then
        echo "✅ 防火墙规则存在"
        sudo ufw status | grep "$port"
    else
        echo "❌ 防火墙规则不存在"
    fi
    
    # 测试本地连接
    echo ""
    echo "🔗 测试本地连接:"
    timeout 3 bash -c "</dev/tcp/localhost/$port" 2>/dev/null && echo "✅ 本地连接成功" || echo "❌ 本地连接失败"
}

# 显示 Dify 相关端口
show_dify_ports() {
    echo "🚀 Dify 服务端口配置:"
    echo "===================="
    echo "端口    | 服务           | 状态"
    echo "--------|----------------|--------"
    
    local ports=("8380:Nginx主端口" "8381:Console API" "15432:PostgreSQL" "5001:API直接" "16379:Redis")
    
    for port_info in "${ports[@]}"; do
        local port=$(echo "$port_info" | cut -d: -f1)
        local service=$(echo "$port_info" | cut -d: -f2)
        
        # 检查是否在监听
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            local listen_status="🟢监听"
        else
            local listen_status="🔴未监听"
        fi
        
        # 检查防火墙规则
        if sudo ufw status 2>/dev/null | grep -q "$port"; then
            local fw_status="🟢允许"
        else
            local fw_status="🔴拒绝"
        fi
        
        printf "%-8s| %-14s | %s %s\n" "$port" "$service" "$listen_status" "$fw_status"
    done
    
    echo ""
    echo "💡 测试命令:"
    echo "  curl http://10.118.1.153:8380"
    echo "  curl http://10.118.1.153:8381"
    echo "  telnet 10.118.1.153 15432"
}

# 主程序
case "${1:-help}" in
    "status")
        check_status
        ;;
    "list")
        list_rules
        ;;
    "enable")
        enable_firewall
        ;;
    "disable")
        disable_firewall
        ;;
    "allow")
        allow_port "$2"
        ;;
    "deny")
        deny_port "$2"
        ;;
    "delete")
        delete_rule "$2"
        ;;
    "reset")
        reset_firewall
        ;;
    "test")
        test_port "$2"
        ;;
    "dify-ports")
        show_dify_ports
        ;;
    "help"|*)
        show_help
        ;;
esac
