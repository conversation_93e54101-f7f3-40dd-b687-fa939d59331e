#!/bin/bash

# 静态路由管理脚本
# ================

set -e

echo "🛣️  静态路由管理工具"

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请以 root 用户运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

# 显示当前路由表
show_routes() {
    echo "📊 当前路由表:"
    echo "=============="
    route -n
    echo ""
}

# 测试路由连通性
test_connectivity() {
    local target="$1"
    echo "🔍 测试 $target 的连通性..."
    
    # Ping 测试
    if ping -c 2 -W 3 "$target" >/dev/null 2>&1; then
        echo "  ✅ Ping 成功"
    else
        echo "  ❌ Ping 失败"
    fi
    
    # 端口扫描
    echo "  🔍 扫描常用端口..."
    for port in 22 80 443 3306 5432; do
        if timeout 3 bash -c "</dev/tcp/$target/$port" 2>/dev/null; then
            echo "    ✅ 端口 $port 开放"
        fi
    done
}

# 备份当前路由
backup_routes() {
    echo "📁 备份当前路由表..."
    route -n > "/tmp/routes_backup_$(date +%Y%m%d_%H%M%S).txt"
    echo "✅ 路由表已备份到 /tmp/"
}

# 删除指定路由
delete_route() {
    local destination="$1"
    local gateway="$2"
    local netmask="$3"
    
    echo "🗑️  删除路由: $destination via $gateway"
    
    if [ "$netmask" = "***************" ]; then
        # 主机路由
        ip route del "$destination" via "$gateway" 2>/dev/null || route del -host "$destination" gw "$gateway" 2>/dev/null
    else
        # 网络路由
        ip route del "$destination/$netmask" via "$gateway" 2>/dev/null || route del -net "$destination" netmask "$netmask" gw "$gateway" 2>/dev/null
    fi
    
    if [ $? -eq 0 ]; then
        echo "  ✅ 路由删除成功"
    else
        echo "  ❌ 路由删除失败"
    fi
}

# 恢复路由
restore_route() {
    local destination="$1"
    local gateway="$2"
    local netmask="$3"
    
    echo "🔄 恢复路由: $destination via $gateway"
    
    if [ "$netmask" = "***************" ]; then
        # 主机路由
        ip route add "$destination" via "$gateway" 2>/dev/null || route add -host "$destination" gw "$gateway" 2>/dev/null
    else
        # 网络路由  
        ip route add "$destination/$netmask" via "$gateway" 2>/dev/null || route add -net "$destination" netmask "$netmask" gw "$gateway" 2>/dev/null
    fi
    
    if [ $? -eq 0 ]; then
        echo "  ✅ 路由恢复成功"
    else
        echo "  ❌ 路由恢复失败"
    fi
}

# 主菜单
show_menu() {
    echo ""
    echo "📋 操作菜单:"
    echo "============"
    echo "1. 显示当前路由表"
    echo "2. 测试路由连通性"
    echo "3. 备份路由表"
    echo "4. 删除 ************/24 路由"
    echo "5. 删除 ************/32 路由"
    echo "6. 删除 ************/32 路由"
    echo "7. 删除所有 172.18.x.x 路由"
    echo "8. 恢复路由（需要手动输入）"
    echo "9. 退出"
    echo ""
}

# 主程序
main() {
    echo "⚠️  警告: 删除路由可能影响网络连通性！"
    echo "建议先测试连通性，确认不需要这些路由后再删除。"
    echo ""
    
    while true; do
        show_menu
        read -p "请选择操作 (1-9): " choice
        
        case $choice in
            1)
                show_routes
                ;;
            2)
                echo "🔍 测试路由连通性..."
                test_connectivity "172.18.139.1"
                test_connectivity "************"
                test_connectivity "************"
                ;;
            3)
                backup_routes
                ;;
            4)
                echo "⚠️  即将删除 ************/24 路由"
                read -p "确认删除？(y/N): " -n 1 -r
                echo
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    backup_routes
                    delete_route "************" "10.118.1.129" "255.255.255.0"
                    show_routes
                fi
                ;;
            5)
                echo "⚠️  即将删除 ************/32 路由"
                read -p "确认删除？(y/N): " -n 1 -r
                echo
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    backup_routes
                    delete_route "************" "10.118.1.129" "***************"
                    show_routes
                fi
                ;;
            6)
                echo "⚠️  即将删除 ************/32 路由"
                read -p "确认删除？(y/N): " -n 1 -r
                echo
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    backup_routes
                    delete_route "************" "10.118.1.129" "***************"
                    show_routes
                fi
                ;;
            7)
                echo "⚠️  即将删除所有 172.18.x.x 路由"
                echo "这将删除以下路由:"
                route -n | grep "172.18"
                echo ""
                read -p "确认删除所有？(y/N): " -n 1 -r
                echo
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    backup_routes
                    delete_route "************" "10.118.1.129" "255.255.255.0"
                    delete_route "************" "10.118.1.129" "***************"
                    delete_route "************" "10.118.1.129" "***************"
                    show_routes
                fi
                ;;
            8)
                echo "🔄 手动恢复路由"
                read -p "输入目标地址: " dest
                read -p "输入网关地址: " gw
                read -p "输入子网掩码 (默认 ***************): " mask
                mask=${mask:-***************}
                restore_route "$dest" "$gw" "$mask"
                show_routes
                ;;
            9)
                echo "👋 退出"
                exit 0
                ;;
            *)
                echo "❌ 无效选择"
                ;;
        esac
        
        echo ""
        read -p "按 Enter 继续..."
    done
}

# 运行主程序
main
