-- PostgreSQL 初始化脚本 for Dify
-- 创建 dify_root 用户和 dify_db 数据库

-- 创建用户 dify_root（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'dify_root') THEN
        CREATE ROLE dify_root WITH LOGIN PASSWORD 'dify_root@ctsy';
    END IF;
END
$$;

-- 授予用户创建数据库的权限
ALTER ROLE dify_root CREATEDB;

-- 创建数据库 dify_db（如果不存在）
SELECT 'CREATE DATABASE dify_db OWNER dify_root'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'dify_db')\gexec

-- 连接到 dify_db 数据库并设置权限
\c dify_db

-- 授予 dify_root 用户对 dify_db 数据库的所有权限
GRANT ALL PRIVILEGES ON DATABASE dify_db TO dify_root;

-- 授予 dify_root 用户对 public schema 的所有权限
GRANT ALL ON SCHEMA public TO dify_root;

-- 设置默认权限，确保 dify_root 可以创建表和其他对象
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO dify_root;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO dify_root;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO dify_root;

-- 创建一些常用的扩展（如果需要）
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 输出确认信息
\echo 'Database dify_db and user dify_root have been created successfully!'
\echo 'Connection details:'
\echo '  Host: localhost (or container name: db)'
\echo '  Port: 15432'
\echo '  Database: dify_db'
\echo '  Username: dify_root'
\echo '  Password: dify_root@ctsy'
