#!/bin/bash

# Ubuntu 24.04 Docker 网络重建脚本
# =================================

set -e

echo "🔧 Ubuntu 24.04 - 安全重建 Docker 网络以解决 IP 冲突..."

# 检查 Ubuntu 版本
if ! grep -q "24.04" /etc/os-release 2>/dev/null; then
    echo "⚠️  警告: 此脚本专为 Ubuntu 24.04 优化"
    read -p "继续执行？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请以 root 用户运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 显示当前状态
echo "📊 当前系统信息:"
echo "================"
echo "Ubuntu 版本: $(lsb_release -d | cut -f2)"
echo "Docker 版本: $(docker --version)"
echo "当前用户: $(whoami)"
echo ""

echo "📊 当前网络状态:"
echo "================"
docker network ls
echo ""
echo "当前路由表:"
route -n | grep -E "(172\.18|172\.19)" || echo "无冲突路由"
echo ""

# 备份当前 Docker daemon 配置
echo "📁 备份配置..."
if [ -f /etc/docker/daemon.json ]; then
    cp /etc/docker/daemon.json /etc/docker/daemon.json.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ 已备份 daemon.json"
fi

# 停止所有容器
echo "🛑 停止所有运行的容器..."
if [ "$(docker ps -q)" ]; then
    docker ps -q | xargs -r docker stop
    echo "✅ 所有容器已停止"
else
    echo "✅ 没有运行的容器"
fi

# 停止 Docker 服务
echo "🛑 停止 Docker 服务..."
systemctl stop docker.socket 2>/dev/null || true
systemctl stop docker.service
echo "✅ Docker 服务已停止"

# 等待服务完全停止
sleep 3

# 清理网络接口
echo "🧹 清理冲突的网络接口..."
# Ubuntu 24.04 可能需要更强制的清理
for bridge in $(ip link show | grep -o 'br-[a-f0-9]\{12\}' | head -10); do
    echo "删除网桥: $bridge"
    ip link set $bridge down 2>/dev/null || true
    ip link delete $bridge 2>/dev/null || true
done

# 清理特定的冲突网桥
for bridge in br-6cd6540c2498 br-b3fe77758c9c br-b20439fd286e br-872a9ab5fc35; do
    if ip link show $bridge &>/dev/null; then
        echo "删除冲突网桥: $bridge"
        ip link set $bridge down 2>/dev/null || true
        ip link delete $bridge 2>/dev/null || true
    fi
done

# 清理 iptables 规则（Ubuntu 24.04 可能有更多规则）
echo "🧹 清理相关的 iptables 规则..."
iptables -t nat -F DOCKER 2>/dev/null || true
iptables -t filter -F DOCKER 2>/dev/null || true
iptables -t filter -F DOCKER-ISOLATION-STAGE-1 2>/dev/null || true
iptables -t filter -F DOCKER-ISOLATION-STAGE-2 2>/dev/null || true
iptables -t filter -F DOCKER-USER 2>/dev/null || true

# 配置新的 Docker daemon 设置
echo "⚙️  配置新的 Docker daemon 设置..."
mkdir -p /etc/docker
cat > /etc/docker/daemon.json << 'EOF'
{
  "default-address-pools": [
    {
      "base": "***********/16",
      "size": 24
    },
    {
      "base": "**********/16", 
      "size": 24
    }
  ],
  "bip": "*************/24",
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2"
}
EOF

echo "✅ Docker daemon 配置已更新"

# 启动 Docker 服务
echo "🔄 启动 Docker 服务..."
systemctl start docker.service
systemctl enable docker.service

# 等待 Docker 启动
echo "⏳ 等待 Docker 服务启动..."
timeout=30
counter=0
while [ $counter -lt $timeout ]; do
    if systemctl is-active --quiet docker; then
        echo "✅ Docker 服务启动成功"
        break
    fi
    echo "⏳ 等待 Docker 启动... ($counter/$timeout)"
    sleep 2
    counter=$((counter + 2))
done

if [ $counter -ge $timeout ]; then
    echo "❌ Docker 启动超时"
    systemctl status docker
    exit 1
fi

# 验证新配置
echo "🔍 验证新配置..."
echo ""
echo "新的 Docker 网络:"
docker network ls
echo ""
echo "新的路由表:"
route -n | grep -E "(192\.168|10\.200)" || echo "新路由尚未生效，这是正常的"
echo ""

# 重建 Dify 服务
if [ -f "docker-compose.yaml" ] || [ -f "docker-compose.yml" ]; then
    echo "🚀 重建 Dify 服务..."
    
    # 使用正确的 compose 文件
    COMPOSE_FILE="docker-compose.yaml"
    [ -f "docker-compose.yml" ] && COMPOSE_FILE="docker-compose.yml"
    
    docker-compose -f "$COMPOSE_FILE" up -d
    
    echo "⏳ 等待服务启动..."
    sleep 20
    
    echo "📊 新的网络状态:"
    docker network ls
    echo ""
    echo "容器状态:"
    docker-compose -f "$COMPOSE_FILE" ps
else
    echo "⚠️  未找到 docker-compose.yaml，请手动启动服务"
fi

echo ""
echo "🎉 Ubuntu 24.04 网络重建完成！"
echo "==============================="
echo "新的 IP 段配置:"
echo "  - Docker 默认网桥: *************/24"
echo "  - 容器网络池: ***********/16 和 **********/16"
echo ""
echo "✅ IP 冲突已解决！"
echo ""
echo "🔍 验证路由表:"
echo "=============="
route -n | grep -E "(192\.168|10\.200)" || echo "新路由将在容器启动后生效"
echo ""
echo "💡 现在可以测试:"
echo "  从 ************ 执行: telnet ************ 8380"
echo ""
echo "📋 如需回滚:"
echo "  sudo cp /etc/docker/daemon.json.backup.* /etc/docker/daemon.json"
echo "  sudo systemctl restart docker"
