#!/bin/bash

# 安全的 Docker 网络重建脚本
# ===========================

set -e

echo "🔧 安全重建 Docker 网络以解决 IP 冲突..."

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请以 root 用户运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

# 显示当前状态
echo "📊 当前网络状态:"
echo "================"
docker network ls
echo ""
echo "当前路由表:"
route -n | grep -E "(172\.18|172\.19)"
echo ""

# 备份当前 Docker daemon 配置
echo "📁 备份配置..."
if [ -f /etc/docker/daemon.json ]; then
    cp /etc/docker/daemon.json /etc/docker/daemon.json.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ 已备份 daemon.json"
fi

# 停止所有容器
echo "🛑 停止所有运行的容器..."
docker ps -q | xargs -r docker stop
echo "✅ 所有容器已停止"

# 停止 Docker 服务
echo "🛑 停止 Docker 服务..."
systemctl stop docker

# 清理网络接口
echo "🧹 清理冲突的网络接口..."
ip link set br-6cd6540c2498 down 2>/dev/null || true
ip link set br-b3fe77758c9c down 2>/dev/null || true
ip link delete br-6cd6540c2498 2>/dev/null || true
ip link delete br-b3fe77758c9c 2>/dev/null || true

# 清理 iptables 规则
echo "🧹 清理相关的 iptables 规则..."
iptables -t nat -F DOCKER 2>/dev/null || true
iptables -t filter -F DOCKER 2>/dev/null || true
iptables -t filter -F DOCKER-ISOLATION-STAGE-1 2>/dev/null || true
iptables -t filter -F DOCKER-ISOLATION-STAGE-2 2>/dev/null || true

# 配置新的 Docker daemon 设置
echo "⚙️  配置新的 Docker daemon 设置..."
mkdir -p /etc/docker
cat > /etc/docker/daemon.json << 'EOF'
{
  "default-address-pools": [
    {
      "base": "***********/16",
      "size": 24
    },
    {
      "base": "**********/16", 
      "size": 24
    }
  ],
  "bip": "*************/24"
}
EOF

echo "✅ Docker daemon 配置已更新"

# 启动 Docker 服务
echo "🔄 启动 Docker 服务..."
systemctl start docker
systemctl enable docker

# 等待 Docker 启动
echo "⏳ 等待 Docker 服务启动..."
sleep 10

# 验证新配置
echo "🔍 验证新配置..."
echo ""
echo "新的 Docker 网络:"
docker network ls
echo ""
echo "新的路由表:"
route -n | grep -E "(192\.168|10\.200)" || echo "暂无新的路由"
echo ""

# 重建 Dify 服务
if [ -f "docker-compose.yaml" ]; then
    echo "🚀 重建 Dify 服务..."
    docker-compose up -d
    
    echo "⏳ 等待服务启动..."
    sleep 15
    
    echo "📊 新的网络状态:"
    docker network ls
    echo ""
    echo "容器状态:"
    docker-compose ps
else
    echo "⚠️  未找到 docker-compose.yaml，请手动启动服务"
fi

echo ""
echo "🎉 网络重建完成！"
echo "=================="
echo "新的 IP 段配置:"
echo "  - Docker 默认网桥: *************/24"
echo "  - 容器网络池: ***********/16 和 **********/16"
echo ""
echo "✅ IP 冲突已解决！"
echo ""
echo "🔍 验证路由表:"
echo "=============="
route -n | grep -E "(192\.168|10\.200)" || echo "新路由尚未生效"
echo ""
echo "💡 现在可以测试:"
echo "  从 ************ 执行: telnet ************ 8380"
