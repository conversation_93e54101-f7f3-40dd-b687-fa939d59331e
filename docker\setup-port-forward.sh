#!/bin/bash

# Dify 端口转发设置脚本
# 将 8381 端口转发到 8380 端口

set -e

echo "🔄 设置端口转发: 8381 → 8380"

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请以 root 用户运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

# 启用 IP 转发
echo "📡 启用 IP 转发..."
echo 1 > /proc/sys/net/ipv4/ip_forward

# 添加 iptables 规则
echo "🔧 添加 iptables 规则..."

# 清除现有规则（可选）
iptables -t nat -D PREROUTING -p tcp --dport 8381 -j REDIRECT --to-port 8380 2>/dev/null || true
iptables -t nat -D OUTPUT -p tcp --dport 8381 -j REDIRECT --to-port 8380 2>/dev/null || true

# 添加新规则
iptables -t nat -A PREROUTING -p tcp --dport 8381 -j REDIRECT --to-port 8380
iptables -t nat -A OUTPUT -p tcp --dport 8381 -j REDIRECT --to-port 8380

echo "✅ 端口转发设置完成"
echo "现在访问 8381 端口会自动转发到 8380 端口"

# 显示当前规则
echo ""
echo "📋 当前 NAT 规则:"
iptables -t nat -L -n --line-numbers

# 保存规则（Ubuntu/Debian）
if command -v iptables-save >/dev/null 2>&1; then
    echo ""
    echo "💾 保存 iptables 规则..."
    iptables-save > /etc/iptables/rules.v4 2>/dev/null || echo "⚠️  无法保存规则，重启后需要重新运行此脚本"
fi

echo ""
echo "🎉 设置完成！"
echo "测试命令: curl http://************:8381"
