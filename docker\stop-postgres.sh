#!/bin/bash

# Dify PostgreSQL 独立部署停止脚本
# ================================

set -e

echo "🛑 停止 Dify PostgreSQL 服务..."

# 停止服务
docker-compose -f docker-compose-pg.yaml down

echo "✅ PostgreSQL 服务已停止"

# 询问是否删除数据
read -p "是否删除数据卷？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  删除数据卷..."
    docker-compose -f docker-compose-pg.yaml down -v
    rm -rf ./volumes/postgres/data/*
    echo "✅ 数据卷已删除"
else
    echo "📁 数据卷已保留"
fi
