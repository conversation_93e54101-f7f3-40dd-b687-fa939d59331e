version: '3.8'

services:
  # PostgreSQL 15 Database for Dify
  db:
    image: postgres:15-alpine
    container_name: dify-postgres
    restart: always
    environment:
      # PostgreSQL 超级用户密码
      POSTGRES_PASSWORD: ${POSTGRES_ADMIN_PASSWORD:-postgres123456}
      # 初始数据库（将通过初始化脚本创建 dify_db）
      POSTGRES_DB: postgres
      # 数据目录
      PGDATA: ${PGDATA:-/var/lib/postgresql/data/pgdata}
      # 应用数据库配置（用于初始化脚本）
      DIFY_DB_NAME: ${DB_DATABASE:-dify_db}
      DIFY_DB_USER: ${DB_USERNAME:-dify_root}
      DIFY_DB_PASSWORD: ${DB_PASSWORD:-dify_root@ctsy}
    command: >
      postgres -c 'max_connections=${POSTGRES_MAX_CONNECTIONS:-100}'
               -c 'shared_buffers=${POSTGRES_SHARED_BUFFERS:-128MB}'
               -c 'work_mem=${POSTGRES_WORK_MEM:-4MB}'
               -c 'maintenance_work_mem=${POSTGRES_MAINTENANCE_WORK_MEM:-64MB}'
               -c 'effective_cache_size=${POSTGRES_EFFECTIVE_CACHE_SIZE:-4096MB}'
               -c 'log_statement=all'
               -c 'log_destination=stderr'
               -c 'logging_collector=on'
               -c 'log_directory=/var/log/postgresql'
               -c 'log_filename=postgresql-%Y-%m-%d_%H%M%S.log'
    volumes:
      # 数据持久化
      - ${PGDATA_HOST_VOLUME:-./volumes/postgres/data}:/var/lib/postgresql/data
      # 日志目录
      - ${PGLOG_HOST_VOLUME:-./volumes/postgres/logs}:/var/log/postgresql
      # 初始化脚本目录
      - ${PGINIT_HOST_VOLUME:-./volumes/postgres/init}:/docker-entrypoint-initdb.d
    ports:
      - "${EXPOSE_POSTGRES_PORT:-15432}:5432"
    healthcheck:
      test: [ 'CMD', 'pg_isready', '-h', 'localhost', '-U', '${DB_USERNAME:-dify_root}', '-d', '${DB_DATABASE:-dify_db}' ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - postgres-network

networks:
  postgres-network:
    driver: bridge

volumes:
  postgres-data:
    driver: local
  postgres-logs:
    driver: local
