#!/bin/bash

# Docker 网络 IP 冲突修复脚本
# =============================

set -e

echo "🔧 修复 Docker 网络 IP 冲突..."

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请以 root 用户运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

# 备份当前 Docker daemon 配置
echo "📁 备份当前配置..."
if [ -f /etc/docker/daemon.json ]; then
    cp /etc/docker/daemon.json /etc/docker/daemon.json.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ 已备份到 /etc/docker/daemon.json.backup.$(date +%Y%m%d_%H%M%S)"
fi

# 停止 Docker 服务
echo "🛑 停止 Docker 服务..."
systemctl stop docker

# 停止所有容器（如果有运行的）
echo "📦 停止所有容器..."
docker ps -q | xargs -r docker stop

# 删除冲突的网络
echo "🗑️  删除冲突的网络..."
docker network rm docker_default 2>/dev/null || echo "网络 docker_default 不存在"
docker network rm docker_ssrf_proxy_network 2>/dev/null || echo "网络 docker_ssrf_proxy_network 不存在"

# 清理网络接口（使用实际的网桥名称）
echo "🧹 清理网络接口..."
ip link delete br-6cd6540c2498 2>/dev/null || echo "网桥 br-6cd6540c2498 不存在"
ip link delete br-b3fe77758c9c 2>/dev/null || echo "网桥 br-b3fe77758c9c 不存在"

# 应用新的 Docker daemon 配置
echo "⚙️  应用新的 Docker daemon 配置..."
mkdir -p /etc/docker
cat > /etc/docker/daemon.json << 'EOF'
{
  "default-address-pools": [
    {
      "base": "***********/16",
      "size": 24
    },
    {
      "base": "**********/16", 
      "size": 24
    }
  ],
  "bip": "*************/24"
}
EOF

echo "✅ Docker daemon 配置已更新"

# 重启 Docker 服务
echo "🔄 重启 Docker 服务..."
systemctl start docker
systemctl enable docker

# 等待 Docker 启动
echo "⏳ 等待 Docker 服务启动..."
sleep 5

# 验证配置
echo "🔍 验证新配置..."
docker network ls
echo ""
echo "📊 新的网络配置:"
ip route | grep docker || echo "没有找到 docker 相关路由"
ip route | grep br- || echo "没有找到 br- 相关路由"

echo ""
echo "🎉 Docker 网络 IP 冲突修复完成！"
echo ""
echo "📋 新的 IP 段配置:"
echo "  - Docker 默认网桥: *************/24"
echo "  - 容器网络池: ***********/16 和 **********/16"
echo ""
echo "⚠️  注意: 需要重新创建和启动您的容器"
echo "建议运行: docker-compose down && docker-compose up -d"
