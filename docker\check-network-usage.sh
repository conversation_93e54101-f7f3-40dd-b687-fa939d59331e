#!/bin/bash

# Docker 网络使用情况检查脚本
# ===========================

echo "🔍 检查 Docker 网络使用情况..."

echo "📊 当前网络列表:"
echo "================"
docker network ls
echo ""

echo "🔗 网络详细信息:"
echo "================"

# 检查 docker_default 网络
echo "1. docker_default 网络:"
docker network inspect docker_default --format '{{.Name}}: {{.IPAM.Config}}' 2>/dev/null || echo "网络不存在"
echo "连接的容器:"
docker network inspect docker_default --format '{{range $key, $value := .Containers}}{{$value.Name}} {{end}}' 2>/dev/null || echo "无容器连接"
echo ""

# 检查 docker_ssrf_proxy_network 网络
echo "2. docker_ssrf_proxy_network 网络:"
docker network inspect docker_ssrf_proxy_network --format '{{.Name}}: {{.IPAM.Config}}' 2>/dev/null || echo "网络不存在"
echo "连接的容器:"
docker network inspect docker_ssrf_proxy_network --format '{{range $key, $value := .Containers}}{{$value.Name}} {{end}}' 2>/dev/null || echo "无容器连接"
echo ""

echo "📦 当前运行的容器:"
echo "=================="
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo ""

echo "🌐 网络接口状态:"
echo "================"
echo "br-6cd6540c2498 (**********):"
ip addr show br-6cd6540c2498 2>/dev/null | grep -E "(inet |UP|DOWN)" || echo "接口不存在"
echo ""
echo "br-b3fe77758c9c (**********):"
ip addr show br-b3fe77758c9c 2>/dev/null | grep -E "(inet |UP|DOWN)" || echo "接口不存在"
echo ""

echo "📋 路由表中的冲突:"
echo "=================="
echo "Docker 网络路由:"
route -n | grep -E "(172\.18|172\.19)"
echo ""
echo "物理网络路由:"
route -n | grep "10\.118\.1"
echo ""

echo "⚠️  IP 冲突分析:"
echo "================"
echo "Docker 使用的 IP 段:"
echo "  - br-6cd6540c2498: **********/16"
echo "  - br-b3fe77758c9c: **********/16"
echo ""
echo "物理网络使用的 IP 段:"
echo "  - ************/24"
echo "  - ************/32"
echo "  - ************/32"
echo ""
echo "🚨 确认存在 IP 段冲突！"
echo ""

echo "💡 建议操作:"
echo "============"
echo "1. 如果容器都已停止，可以安全删除这些网络"
echo "2. 运行 safe-network-rebuild.sh 重建网络"
echo "3. 或者手动重新配置 Docker daemon"
