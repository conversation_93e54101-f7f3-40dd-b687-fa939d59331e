#!/bin/bash

# Dify PostgreSQL 独立部署启动脚本
# ================================

set -e

echo "🚀 启动 Dify PostgreSQL 服务..."

# 检查必要的目录是否存在
echo "📁 检查并创建必要的目录..."
mkdir -p ./volumes/postgres/data
mkdir -p ./volumes/postgres/logs
mkdir -p ./volumes/postgres/init

# 设置目录权限（PostgreSQL 需要特定权限）
echo "🔐 设置目录权限..."
chmod 755 ./volumes/postgres/data
chmod 755 ./volumes/postgres/logs
chmod 755 ./volumes/postgres/init

# 检查初始化脚本是否存在
if [ ! -f "./volumes/postgres/init/01-init-dify-db.sql" ]; then
    echo "❌ 初始化脚本不存在，请确保 01-init-dify-db.sql 文件在 ./volumes/postgres/init/ 目录中"
    exit 1
fi

# 启动 PostgreSQL 服务
echo "🐘 启动 PostgreSQL 容器..."
docker-compose -f docker-compose-pg.yaml --env-file postgres.env up -d

# 等待服务启动
echo "⏳ 等待 PostgreSQL 服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose -f docker-compose-pg.yaml ps

# 等待健康检查通过
echo "🏥 等待健康检查通过..."
timeout=60
counter=0
while [ $counter -lt $timeout ]; do
    if docker-compose -f docker-compose-pg.yaml ps | grep -q "healthy"; then
        echo "✅ PostgreSQL 服务启动成功！"
        break
    fi
    echo "⏳ 等待健康检查通过... ($counter/$timeout)"
    sleep 2
    counter=$((counter + 2))
done

if [ $counter -ge $timeout ]; then
    echo "❌ 健康检查超时，请检查服务状态"
    docker-compose -f docker-compose-pg.yaml logs db
    exit 1
fi

# 显示连接信息
echo ""
echo "🎉 PostgreSQL 服务部署完成！"
echo "================================"
echo "连接信息："
echo "  主机: localhost"
echo "  端口: 15432"
echo "  数据库: dify_db"
echo "  用户名: dify_root"
echo "  密码: dify_root@ctsy"
echo ""
echo "管理员连接信息："
echo "  用户名: postgres"
echo "  密码: postgres123456"
echo ""
echo "测试连接命令："
echo "  PGPASSWORD='dify_root@ctsy' psql -h localhost -p 15432 -U dify_root -d dify_db"
echo ""
echo "查看日志："
echo "  docker-compose -f docker-compose-pg.yaml logs -f db"
echo ""
echo "停止服务："
echo "  docker-compose -f docker-compose-pg.yaml down"
echo "================================"
