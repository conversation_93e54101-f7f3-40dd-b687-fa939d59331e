#!/bin/bash

# Docker 网络重建脚本
# ===================

set -e

echo "🔄 重建 Docker 网络..."

# 检查当前目录是否有 docker-compose.yaml
if [ ! -f "docker-compose.yaml" ]; then
    echo "❌ 未找到 docker-compose.yaml 文件"
    echo "请在包含 docker-compose.yaml 的目录中运行此脚本"
    exit 1
fi

# 显示当前网络状态
echo "📊 当前网络状态:"
docker network ls
echo ""

# 停止所有服务
echo "🛑 停止所有 Docker Compose 服务..."
docker-compose down

# 删除特定的冲突网络
echo "🗑️  删除冲突的网络..."
docker network rm docker_default 2>/dev/null || echo "网络 docker_default 不存在或已删除"
docker network rm docker_ssrf_proxy_network 2>/dev/null || echo "网络 docker_ssrf_proxy_network 不存在或已删除"

# 删除其他自定义网络（保留默认网络）
echo "🗑️  删除其他自定义网络..."
docker network ls --format "{{.Name}}" | grep -v -E "^(bridge|host|none)$" | xargs -r docker network rm 2>/dev/null || true

# 清理未使用的网络
echo "🧹 清理未使用的网络..."
docker network prune -f

# 重新创建网络并启动服务
echo "🚀 重新创建网络并启动服务..."
docker-compose up -d

# 显示新的网络配置
echo ""
echo "📊 新的网络配置:"
echo "=================="
docker network ls
echo ""
echo "🔍 网络详情:"
docker network ls --format "{{.Name}}" | grep -v -E "^(bridge|host|none)$" | while read network; do
    echo "网络: $network"
    docker network inspect "$network" | grep -E "(Subnet|Gateway)" | head -2
    echo "---"
done

echo ""
echo "📋 路由表:"
ip route | grep -E "(docker|br-)"

echo ""
echo "✅ 网络重建完成！"
