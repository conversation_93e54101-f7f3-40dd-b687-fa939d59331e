#!/bin/bash

# Docker 网络重建脚本
# ===================

set -e

echo "🔄 重建 Docker 网络..."

# 检查当前目录是否有 docker-compose.yaml
if [ ! -f "docker-compose.yaml" ]; then
    echo "❌ 未找到 docker-compose.yaml 文件"
    echo "请在包含 docker-compose.yaml 的目录中运行此脚本"
    exit 1
fi

# 停止所有服务
echo "🛑 停止所有 Docker Compose 服务..."
docker-compose down

# 删除所有网络（保留默认网络）
echo "🗑️  删除自定义网络..."
docker network ls --format "{{.Name}}" | grep -v -E "^(bridge|host|none)$" | xargs -r docker network rm

# 清理未使用的网络
echo "🧹 清理未使用的网络..."
docker network prune -f

# 重新创建网络并启动服务
echo "🚀 重新创建网络并启动服务..."
docker-compose up -d

# 显示新的网络配置
echo ""
echo "📊 新的网络配置:"
echo "=================="
docker network ls
echo ""
echo "🔍 网络详情:"
docker network ls --format "{{.Name}}" | grep -v -E "^(bridge|host|none)$" | while read network; do
    echo "网络: $network"
    docker network inspect "$network" | grep -E "(Subnet|Gateway)" | head -2
    echo "---"
done

echo ""
echo "📋 路由表:"
ip route | grep -E "(docker|br-)"

echo ""
echo "✅ 网络重建完成！"
